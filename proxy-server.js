const express = require('express');
const cors = require('cors');
const https = require('https');

// Disable SSL verification for internal APIs
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

const app = express();
const PORT = process.env.PORT || 3001;

app.use(cors());
app.use(express.json());

// Proxy endpoint for forwarding chat requests
app.post('/proxy-chat', async (req, res) => {
  const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));
  try {
    console.log('Received request:', JSON.stringify(req.body, null, 2));
    
    const response = await fetch('https://epro-bot-test.int.electroluxprofessional.com/insecure-chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(req.body),
    });
    
    console.log('Backend response status:', response.status);
    const text = await response.text();
    console.log('Backend response text:', text);
    
    res.status(response.status).send(text);
  } catch (err) {
    console.error('Proxy error:', err);
    res.status(500).json({ error: 'Proxy error', details: err.message });
  }
});

app.listen(PORT, () => {
  console.log(`Proxy server listening on port ${PORT}`);
});
