# Chat Widget Project

A simple chat widget with a Node.js backend and a plug-and-play frontend. The backend supports demo triggers and Azure OpenAI integration. The frontend is a floating chat widget you can embed in any web page.

---

## Features

### Backend (Node.js + Express)

- **API endpoint:** `/api/ask` for chat messages
- **Demo triggers:**
  - `image`: Returns a sample image URL
  - `table`: Returns a sample table as JSON
  - `radio`: Returns a sample multiple-choice question
- **Azure OpenAI integration:** Forwards all other messages to Azure OpenAI and returns the response
- **CORS enabled** for easy frontend integration

### Frontend (Vanilla JS + Tailwind CSS)

- **Floating chat widget** with modern UI
- **Responsive:** Works on desktop and mobile
- **Supports:**
  - Text, image, table, and multiple-choice responses
  - Fullscreen mode
  - Loading indicator

---

## Quick Start

### 1. Install dependencies

```sh
 npm install express axios cors dotenv
```

### 2. Configure environment variables

Create a `.env` file in the root directory:

```env
AZURE_OPENAI_ENDPOINT=your_azure_openai_endpoint
AZURE_OPENAI_KEY=your_azure_openai_key
AZURE_OPENAI_DEPLOYMENT=your_deployment_name
PORT=3000 # optional (3000 is the default)
```

### 3. Start the backend

```sh
node server.js
```

### 4. Open the frontend

Just open `index.html` in your browser. The chat widget will appear in the bottom-right corner.

---

## API Reference

### POST `/api/ask`

**Request:**

```json
{
  "message": "your message here"
}
```

**Response:**

- For demo triggers, returns a special response (see Features)
- Otherwise, returns the Azure OpenAI completion as plain text

---

## File Structure

```
chat-widget.js   # Frontend widget script
index.html       # Demo HTML page
server.js        # Node.js backend
package.json     # Dependencies
.env             # Your secrets (not committed)
README.md        # This file
```

---
